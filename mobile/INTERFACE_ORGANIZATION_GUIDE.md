# Interface Organization Guide

## 🎯 Single Source of Truth Principle

**ALL interfaces should be defined in the `types/` folder, NOT in API files.**

## ✅ Correct Structure

```
mobile/src/
├── types/                    # 📁 SINGLE SOURCE OF TRUTH for all interfaces
│   ├── auth/                # Authentication types
│   ├── api/                 # API request/response types
│   ├── common/              # Shared types
│   ├── food/                # Food domain types
│   └── index.ts             # Main export
├── api/                     # 🔧 API functions ONLY (no interfaces)
│   ├── auth.ts              # Login/register functions
│   ├── meals.api.ts         # Meal API functions
│   └── user.ts              # User API functions
└── screens/                 # 📱 React components
    ├── LoginScreen.tsx
    └── RegisterScreen.tsx
```

## 📝 Import Patterns

### ✅ CORRECT - Import types from types folder
```typescript
// In screens/LoginScreen.tsx
import { loginApi } from '../api/auth';           // API function
import type { LoginData } from '../types/auth';   // Type definition

// In api/auth.ts
import type { LoginData } from '../types/auth';   // Import type
export const loginApi = {
  login: async (data: LoginData) => { ... }       // Use type
};
```

### ❌ WRONG - Don't define interfaces in API files
```typescript
// DON'T DO THIS in api/auth.ts
export interface LoginData {  // ❌ Wrong place
  email: string;
  password: string;
}
```

## 🔄 Migration Steps

1. **Move all interfaces** from `api/` files to appropriate `types/` folders
2. **Update API files** to import types from `types/` folder
3. **Update screens** to import types from `types/` folder
4. **Remove duplicate interfaces** from API files

## 🎯 Benefits

- ✅ **Single source of truth** - No duplicate interfaces
- ✅ **Better organization** - Types grouped by domain
- ✅ **Easier maintenance** - Update types in one place
- ✅ **Better imports** - Clear separation of concerns
- ✅ **Type safety** - Consistent type definitions across app

## 📋 Checklist

- [ ] All interfaces moved to `types/` folder
- [ ] API files only contain functions, not interfaces
- [ ] Screens import types from `types/` folder
- [ ] No duplicate interface definitions
- [ ] All imports use `type` keyword for type-only imports
